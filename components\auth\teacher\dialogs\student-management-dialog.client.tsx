'use client';

import { useState } from 'react';
import { useMutation } from 'blade/client/hooks';
import { useAuth } from '../../../../hooks/useAuth';
import { Dialog } from '@base-ui-components/react/dialog';
import { UserPlus, X } from 'lucide-react';
import { cn } from '../../../../lib/utils';

interface StudentManagementDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function StudentManagementDialog({ isOpen, onOpenChange }: StudentManagementDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    username: '',
    grade: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const { set } = useMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate required fields
      if (!formData.name.trim() || !formData.email.trim()) {
        alert('Name and email are required');
        setIsLoading(false);
        return;
      }

      // Ensure we have a current teacher
      if (!user || user.role !== 'teacher') {
        throw new Error('Only teachers can add students');
      }

      const username = formData.username || formData.email.split('@')[0];

      // Generate a unique password for this student
      const generateStudentPassword = () => {
        const adjectives = ['Smart', 'Bright', 'Quick', 'Sharp', 'Clever', 'Wise', 'Bold', 'Swift'];
        const nouns = ['Lion', 'Eagle', 'Tiger', 'Wolf', 'Bear', 'Fox', 'Hawk', 'Owl'];
        const numbers = Math.floor(Math.random() * 100).toString().padStart(2, '0');

        const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
        const noun = nouns[Math.floor(Math.random() * nouns.length)];

        return `${adjective}${noun}${numbers}`;
      };

      const uniquePassword = generateStudentPassword();

      // Ensure username is valid
      if (!username) {
        throw new Error('Username could not be generated from email');
      }

      // Step 1: Create student using custom API (handles Better Auth + user record)
      console.log('Creating student with custom API...');
      console.log('Current user role:', user.role);

      const response = await fetch('/api/create-student', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: formData.email,
          password: uniquePassword,
          name: formData.name,
          username: username,
          teacherId: user.id,
          grade: formData.grade || null
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('Student creation API error:', errorData);
        throw new Error(errorData.error || 'Failed to create student account');
      }

      const result = await response.json();
      console.log('Student created successfully via API:', result);

      // Step 2: Trigger a Blade mutation to ensure UI updates instantly
      // This is a "dummy" mutation that forces Blade to revalidate the users query
      await set.users({
        with: { id: result.user.id },
        to: { updatedAt: new Date().toISOString() }
      });

      console.log('UI update triggered via Blade mutation');

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('studentCreated', {
        detail: { student: result.user }
      }));

      // Reset form and close dialog
      setFormData({
        name: '',
        email: '',
        username: '',
        grade: ''
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating student:', error);
      alert('Failed to create student. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black/50 opacity-100 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 z-[100000]" />
        <Dialog.Popup className="fixed top-1/2 left-1/2 -mt-8 w-[500px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white dark:bg-gray-800 p-6 text-gray-900 dark:text-gray-100 outline-1 outline-gray-200 dark:outline-gray-700 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100001]">
          <Dialog.Title className="-mt-1.5 mb-1 text-lg font-medium flex items-center gap-2">
            <UserPlus className="w-5 h-5" />
            Add New Student
          </Dialog.Title>
          <Dialog.Description className="mb-6 text-base text-gray-600 dark:text-gray-400">
            Add a new student to your class and send them an invitation to join.
          </Dialog.Description>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="student-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Student Name
              </label>
              <input
                id="student-name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter student's full name"
                required
              />
            </div>

            <div>
              <label htmlFor="student-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email Address
              </label>
              <input
                id="student-email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label htmlFor="student-username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Username (for login)
              </label>
              <input
                id="student-username"
                type="text"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="student_username"
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Leave empty to auto-generate from email. Students use this to log in.
              </p>
            </div>

            <div>
              <label htmlFor="student-grade" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Grade Level
              </label>
              <select
                id="student-grade"
                value={formData.grade}
                onChange={(e) => handleInputChange('grade', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
              >
                <option value="">Select grade level</option>
                <option value="9">9th Grade</option>
                <option value="10">10th Grade</option>
                <option value="11">11th Grade</option>
                <option value="12">12th Grade</option>
              </select>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Dialog.Close className="flex h-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-3.5 text-base font-medium text-gray-900 dark:text-gray-100 select-none hover:bg-gray-100 dark:hover:bg-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-blue-800 active:bg-gray-100 dark:active:bg-gray-600">
                Cancel
              </Dialog.Close>
              <button
                type="submit"
                disabled={isLoading}
                className={cn(
                  "flex h-10 items-center justify-center rounded-md px-3.5 text-base font-medium text-white select-none focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-blue-800",
                  isLoading
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700 active:bg-blue-700"
                )}
              >
                {isLoading ? 'Creating Student...' : 'Add Student'}
              </button>
            </div>
          </form>

          {/* Close button in top right */}
          <Dialog.Close className="absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-none disabled:pointer-events-none">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Dialog.Close>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
}

// Dialog Trigger Button Component
interface StudentManagementDialogTriggerProps {
  className?: string;
  variant?: 'icon' | 'text' | 'button';
  children?: React.ReactNode;
}

export function StudentManagementDialogTrigger({
  className,
  variant = 'icon',
  children
}: StudentManagementDialogTriggerProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Default icon variant
  if (variant === 'icon') {
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className={cn(
            "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-7 w-7",
            className
          )}
          aria-label="Add new student"
        >
          <UserPlus className="h-4 w-4" />
        </button>

        <StudentManagementDialog
          isOpen={isOpen}
          onOpenChange={setIsOpen}
        />
      </>
    );
  }

  // Button variant for styled button
  if (variant === 'button') {
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className={cn(
            "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
            className
          )}
          aria-label="Add new student"
        >
          {children || (
            <>
              <UserPlus className="h-4 w-4" />
              New Student
            </>
          )}
        </button>

        <StudentManagementDialog
          isOpen={isOpen}
          onOpenChange={setIsOpen}
        />
      </>
    );
  }

  // Text variant for header button
  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={cn(
          "inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
          className
        )}
        aria-label="Add new student"
      >
        {children || (
          <>
            <UserPlus className="h-5 w-5" />
            Students
          </>
        )}
      </button>

      <StudentManagementDialog
        isOpen={isOpen}
        onOpenChange={setIsOpen}
      />
    </>
  );
}
